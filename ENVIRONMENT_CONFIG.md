# 环境配置说明

## 路由路径配置

本项目支持根据不同环境自动配置路由路径：

### 本地开发环境
- **环境变量**: `VITE_APP_BASE=dev`
- **访问路径**: 不带 `/platform-landing` 前缀
- **示例**: `http://localhost:3000/`

### 生产环境和其他环境
- **环境变量**: `VITE_APP_BASE=prod` 或其他非 `dev` 值
- **访问路径**: 带 `/platform-landing` 前缀
- **示例**: `https://your-domain.com/platform-landing/`

## 环境变量文件

### .env (开发环境默认)
```
VITE_APP_BASE=dev
VITE_APP_FLUID_MODE=frs
VITE_APP_DEBUG_LOG=debug
```

### .env.production (生产环境)
```
VITE_APP_BASE=prod
VITE_APP_FLUID_MODE=frs
VITE_APP_DEBUG_LOG=error
```

## 运行命令

### 开发环境
```bash
npm start
# 或者
cross-env VITE_APP_BASE=dev vite
```

### 生产环境构建
```bash
npm run build
# 或者
cross-env VITE_APP_BASE=prod vite build
```

## 配置原理

项目中的以下文件会根据 `VITE_APP_BASE` 环境变量自动调整路径：

1. **vite.config.ts**: 配置 Vite 的 base 路径
2. **src/configuration/URL.config.ts**: 配置应用内的 BASE_PATH
3. **src/routes/NoAuthRoutes.ts**: 配置无需认证的路由
4. **src/index.tsx**: 配置认证提供者的 basePath

所有这些配置都遵循相同的逻辑：
- 当 `VITE_APP_BASE !== 'dev'` 时，使用 `/platform-landing` 前缀
- 当 `VITE_APP_BASE === 'dev'` 时，使用 `/` 根路径

## 注意事项

- 确保在不同环境中设置正确的 `VITE_APP_BASE` 环境变量
- 本地开发时建议使用 `VITE_APP_BASE=dev` 以获得最佳开发体验
- 部署到生产环境时确保使用 `VITE_APP_BASE=prod` 或其他非 `dev` 值
