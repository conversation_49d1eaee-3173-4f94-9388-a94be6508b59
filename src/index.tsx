// React Imports
import React from 'react';
import './i18n';
import reportWebVitals from './reportWebVitals';
import { BASE_PATH as URL } from './configuration/URL.config';

// Stryles
import './index.css';
import '@fontsource/open-sans';

// React Router
import { <PERSON>rowserRouter } from 'react-router-dom';

// Fluid Data Helpers
import register from '@nexusplatform/sdc-schema';
import { HexAuthProvider } from '@nexusplatform/react';

import { AUTH_CONFIG as AUTH } from './configuration/AUTH.config';

import RoutesWithConfig from './routes/routes';

import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/lib/integration/react';
import { store, persist } from './redux';

// Register our schemas
register();

import { createRoot } from 'react-dom/client';
import { SnackbarProvider } from '@nexusui/components';
import { Fade } from '@mui/material';
// Add at the beginning of your entry file
import { <PERSON>uffer } from 'buffer';

// Set up global polyfills
window.Buffer = Buffer;
window.global = window;
window.process = window.process || {};
window.process.env = window.process.env || {};

const container = document.getElementById('root');
const root = createRoot(container!);

root.render(
  // <React.StrictMode>
  // Strict Mode in 18 will cause useEffect called twice, commented it here.
  // https://github.com/facebook/react/issues/24553
  <Provider store={store}>
    <PersistGate loading={null} persistor={persist}>
      <BrowserRouter basename={URL}>
        <SnackbarProvider maxSnack={3} anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }} autoHideDuration={6000} TransitionComponent={Fade} hideIconVariant>
          <HexAuthProvider
            authority={AUTH.authority}
            metadataSeed={{
              issuer: AUTH.issuer,
              token_endpoint: `${AUTH.issuer}/protocol/openid-connect/token`
            }}
            clientId={AUTH.clientID}
            routesProtection={false}
            basePath={window.location.origin.includes('localhost') ? window.location.origin : `${window.location.origin}/platform-landing`}
          >
            <RoutesWithConfig />
          </HexAuthProvider>
        </SnackbarProvider>
      </BrowserRouter>
    </PersistGate>
  </Provider>
  // </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
